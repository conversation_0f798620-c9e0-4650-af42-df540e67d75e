<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Inventario
{
	// --- Atributos ---
	private ?int $id                    = null;
	private ?int $id_contenedor         = null;
	private ?int $id_activo             = null;
	private ?string $activo_descripcion = null;
	private ?int $cantidad              = null;
	private ?int $en_contenedor         = null;
	private ?int $id_trabajador         = null;
	private ?string $trabajador_nombre  = null;
	private ?string $trabajador_cedula  = null;
	private ?string $activo_marca       = null;
	private ?string $activo_modelo      = null;
	private ?string $activo_numero_serie = null;
	private ?string $contenedor_descripcion = null;
	
	/**
	 * Constructor: Inicializa las propiedades del objeto Inventario.
	 */
	public function __construct()
	{
		$this->id                 = 0;     // O null si prefieres no usar 0 por defecto
		$this->id_contenedor      = null;
		$this->id_activo          = null;
		$this->activo_descripcion = null;
		$this->cantidad           = 0;     // Valor por defecto 0
		$this->en_contenedor      = 1;     // Valor por defecto 1
		$this->id_trabajador      = null;
		$this->trabajador_nombre  = null;
		$this->trabajador_cedula  = null;
		$this->activo_marca       = null;
		$this->activo_modelo      = null;
		$this->activo_numero_serie = null;
		$this->contenedor_descripcion = null;
	}
	
	/**
	 * Método estático para construir un objeto Inventario desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del inventario.
	 *
	 * @return self Instancia de Inventario.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                     = new self();
			$objeto->id                 = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->id_contenedor      = isset($resultado['id_contenedor']) ? (int)$resultado['id_contenedor'] : null;
			$objeto->id_activo          = isset($resultado['id_activo']) ? (int)$resultado['id_activo'] : null;
			$objeto->activo_descripcion = $resultado['activo_descripcion'] ?? null;
			$objeto->cantidad           = isset($resultado['cantidad']) ? (int)$resultado['cantidad'] : 0;
			$objeto->en_contenedor      = isset($resultado['en_contenedor']) ? (int)$resultado['en_contenedor'] : 1;
			$objeto->id_trabajador      = isset($resultado['id_trabajador']) ? (int)$resultado['id_trabajador'] : null;
			$objeto->trabajador_nombre  = $resultado['trabajador_nombre'] ?? null;
			$objeto->trabajador_cedula  = $resultado['trabajador_cedula'] ?? null;
			$objeto->activo_marca       = $resultado['marca'] ?? null;
			$objeto->activo_modelo      = $resultado['modelo'] ?? null;
			$objeto->activo_numero_serie = $resultado['numero_serie'] ?? null; // Assuming 'numero_serie' is the column name in activos table
			$objeto->contenedor_descripcion = $resultado['contenedor_descripcion'] ?? null;
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Inventario: " . $e->getMessage());
		}
	}
	
	// --- Métodos de Acceso a Datos (Estáticos) ---
	
	/**
	 * Obtiene un registro de inventario por su ID.
	 *
	 * @param int $id       ID del registro de inventario.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Inventario o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener inventario por ID con información del trabajador, activo y contenedor (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	i.*,
            	CONCAT(t.apellidos, ' ', t.nombre) AS trabajador_nombre,
            	t.cedula AS trabajador_cedula,
            	a.marca,
            	a.modelo, a.descripcion AS activo_descripcion,
            	a.numero_serie,
            	c.descripcion AS contenedor_descripcion
            FROM inventario i
            LEFT JOIN trabajadores t ON i.id_trabajador = t.id
            INNER JOIN activos a ON i.id_activo = a.id AND a.estado = 1
            LEFT JOIN contenedores c ON i.id_contenedor = c.id
            WHERE
            	i.id = :id
            LIMIT 1
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			return $resultado ? self::construct($resultado) : null;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener Inventario (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene un registro de inventario específico por contenedor y activo.
	 *
	 * @param int $id_contenedor ID del contenedor.
	 * @param int $id_activo     ID del activo.
	 * @param PDO $conexion      Conexión PDO.
	 *
	 * @return self|null Objeto Inventario o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_by_contenedor_activo(int $id_contenedor, int $id_activo, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener inventario por contenedor y activo con información del trabajador y activo (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	i.*,
            	CONCAT(t.apellidos, ' ', t.nombre) AS trabajador_nombre,
            	t.cedula AS trabajador_cedula,
            	a.marca,
            	a.modelo, a.descripcion AS activo_descripcion,
            	a.numero_serie
            FROM inventario i
            LEFT JOIN trabajadores t ON i.id_trabajador = t.id
            INNER JOIN activos a ON i.id_activo = a.id AND a.estado = 1
            WHERE
            	i.id_contenedor = :id_contenedor AND i.id_activo = :id_activo
            LIMIT 1
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_contenedor", $id_contenedor, PDO::PARAM_INT);
			$statement->bindValue(":id_activo", $id_activo, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			return $resultado ? self::construct($resultado) : null;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener Inventario por contenedor y activo: " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de registros de inventario por activo.
	 *
	 * @param int $id_activo ID del activo.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return array Array de objetos Inventario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_by_activo(int $id_activo, PDO $conexion): array
	{
		// Utilizar el método get_list con el filtro de activo
		return self::get_list($conexion, null, $id_activo);
	}
	
	/**
	 * Obtiene una lista de registros de inventario con filtros opcionales.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param int|null $id_contenedor ID del contenedor (opcional).
	 * @param int|null $id_activo ID del activo (opcional).
	 * @param string|null $texto_activo Texto para buscar en la descripción del activo (opcional).
	 * @param int|null $en_contenedor Filtro para estado en_contenedor (1 = dentro, 0 = fuera) (opcional).
	 * @param int|null $id_trabajador ID del trabajador asignado (-1 = no asignado, >0 = ID específico) (opcional).
	 *
	 * @return array Array de objetos Inventario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(
		PDO $conexion, 
		?int    $id_contenedor = null,
		?int    $id_activo     = null,
		?string $texto_activo  = null,
		?string $en_contenedor = '',
		?int    $id_trabajador = null
	): array
	{
		try {
			// Construir la consulta base con información del trabajador, activo y contenedor
			$query = <<<SQL
            SELECT
            	i.*,
            	CONCAT(t.apellidos, ' ', t.nombre) AS trabajador_nombre,
            	t.cedula AS trabajador_cedula,
            	a.marca,
            	a.modelo, a.descripcion AS activo_descripcion,
            	a.numero_serie,
            	c.descripcion AS contenedor_descripcion
            FROM inventario i
            LEFT JOIN trabajadores t ON i.id_trabajador = t.id
            INNER JOIN activos a ON i.id_activo = a.id AND a.estado = 1
            LEFT JOIN contenedores c ON i.id_contenedor = c.id
            WHERE i.id > 0
            SQL;
			
			// Añadir filtros según los parámetros proporcionados
			$params = [];
			
			// Filtro por contenedor
			if (!empty($id_contenedor)) {
				$query .= " AND i.id_contenedor = :id_contenedor";
				$params[':id_contenedor'] = $id_contenedor;
			}
			
			// Filtro por activo
			if (!empty($id_activo)) {
				$query .= " AND i.id_activo = :id_activo";
				$params[':id_activo'] = $id_activo;
			}
			
			// Filtro por texto de activo (requiere JOIN con activos)
			if (!empty($texto_activo)) {
				// Asegurarse de que el JOIN con activos sea INNER JOIN para este caso
				$query = str_replace(
					"LEFT JOIN activos a ON i.id_activo = a.id",
					"INNER JOIN activos a ON i.id_activo = a.id",
					$query
				);
				$query .= " AND a.descripcion LIKE :texto_activo";
				$params[':texto_activo'] = '%' . $texto_activo . '%';
			}
			
			// Filtro por estado en_contenedor
			if ($en_contenedor == 0 || $en_contenedor == 1) {
				$query .= " AND i.en_contenedor = :en_contenedor";
				$params[':en_contenedor'] = $en_contenedor;
			}
			
			// Filtro por trabajador
			if (!empty($id_trabajador)) {
				if ($id_trabajador == -1) {
					// Filtrar por no asignado (id_trabajador IS NULL)
					$query .= " AND i.id_trabajador IS NULL";
				} else if ($id_trabajador > 0) {
					// Filtrar por trabajador específico
					$query .= " AND i.id_trabajador = :id_trabajador";
					$params[':id_trabajador'] = $id_trabajador;
				}
			}
			
			// Añadir ordenamiento
			$query .= " ORDER BY i.id_contenedor, i.id_activo";
			
			// Preparar y ejecutar la consulta
			$statement = $conexion->prepare($query);
			
			// Bind de parámetros
			foreach ($params as $param => $value) {
				$type = is_int($value) ? PDO::PARAM_INT : PDO::PARAM_STR;
				$statement->bindValue($param, $value, $type);
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			// Construir y devolver la lista de objetos
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Inventario: " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de registros de inventario por contenedor.
	 *
	 * @param int $id_contenedor ID del contenedor.
	 * @param PDO $conexion      Conexión PDO.
	 *
	 * @return array Array de objetos Inventario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_by_contenedor(int $id_contenedor, PDO $conexion): array
	{
		// Utilizar el método get_list con el filtro de contenedor
		return self::get_list($conexion, $id_contenedor);
	}
	
	/**
	 * Crea un nuevo registro de inventario en la base de datos a partir de un objeto Inventario.
	 * El objeto Inventario debe estar completamente poblado con id_contenedor, id_activo y cantidad.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo registro de inventario creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		if ($this->getId_contenedor() === null || $this->getId_activo() === null || $this->getCantidad() === null) {
			throw new Exception("id_contenedor, id_activo y cantidad son requeridos en el objeto Inventario para crearlo.");
		}
		
		// Validar que cantidad sea mayor o igual a 0
		if ($this->getCantidad() < 0) {
			throw new Exception("La cantidad debe ser mayor o igual a 0.");
		}
		
		try {
			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO inventario (
            	 id_contenedor
            	,id_activo
            	,cantidad
            	,en_contenedor
            	,id_trabajador
            ) VALUES (
            	 :id_contenedor
            	,:id_activo
            	,:cantidad
            	,:en_contenedor
            	,:id_trabajador
            )
            SQL;
			
			$statement = $conexion->prepare($query);
			
			// Bind de parámetros desde el objeto
			$statement->bindValue(':id_contenedor', $this->getId_contenedor(), PDO::PARAM_INT);
			$statement->bindValue(':id_activo', $this->getId_activo(), PDO::PARAM_INT);
			$statement->bindValue(':cantidad', $this->getCantidad(), PDO::PARAM_INT);
			$statement->bindValue(':en_contenedor', $this->getEn_contenedor(), PDO::PARAM_INT);
			$statement->bindValue(':id_trabajador', $this->getId_trabajador(), PDO::PARAM_INT);
			
			// Ejecutar la consulta
			$success = $statement->execute();
			
			if ($success) {
				// Devolver el ID del registro recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}
			
		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. violación de restricción única)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al crear inventario: Ya existe un registro para este contenedor y activo.");
			} else {
				throw new Exception("Error de base de datos al crear inventario: " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear inventario: " . $e->getMessage());
		}
	}
	
	/**
	 * Actualiza la cantidad de un registro de inventario existente.
	 *
	 * @param int $id          ID del registro de inventario a modificar.
	 * @param int $nuevaCantidad La nueva cantidad para el registro.
	 * @param PDO $conexion    Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si la nueva cantidad es negativa o si ocurre un error de base de datos.
	 */
	public static function modificar_cantidad(int $id, int $nuevaCantidad, PDO $conexion): bool
	{
		if ($nuevaCantidad < 0) {
			throw new Exception("La cantidad no puede ser negativa.");
		}
		
		try {
			// Consulta para actualizar la cantidad
			$query = <<<SQL
            UPDATE inventario SET
                cantidad = :cantidad
            WHERE
                id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':cantidad', $nuevaCantidad, PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al modificar cantidad de inventario (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Elimina un registro de inventario.
	 *
	 * @param int $id       ID del registro de inventario a eliminar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la eliminación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para eliminar el registro
			$query = <<<SQL
            DELETE FROM inventario
            WHERE
            	id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al eliminar inventario (ID: $id): " . $e->getMessage());
		}
	}
	
	// --- Getters y Setters ---
	
	public function getId(): ?int
	{
		return $this->id;
	}
	
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}
	
	public function getId_contenedor(): ?int
	{
		return $this->id_contenedor;
	}
	
	public function setId_contenedor(?int $id_contenedor): self
	{
		$this->id_contenedor = $id_contenedor;
		return $this;
	}
	
	public function getId_activo(): ?int
	{
		return $this->id_activo;
	}
	
	public function setId_activo(?int $id_activo): self
	{
		$this->id_activo = $id_activo;
		return $this;
	}
	
	public function getCantidad(): ?int
	{
		return $this->cantidad;
	}
	
	public function setCantidad(?int $cantidad): self
	{
		$this->cantidad = $cantidad;
		return $this;
	}
	
	public function getEn_contenedor(): ?int
	{
		return $this->en_contenedor;
	}
	
	public function setEn_contenedor(?int $en_contenedor): self
	{
		$this->en_contenedor = $en_contenedor;
		return $this;
	}
	
	public function getId_trabajador(): ?int
	{
		return $this->id_trabajador;
	}
	
	public function setId_trabajador(?int $id_trabajador): self
	{
		$this->id_trabajador = $id_trabajador;
		return $this;
	}
	
	public function getTrabajador_nombre(): ?string
	{
		return $this->trabajador_nombre;
	}
	
	public function setTrabajador_nombre(?string $trabajador_nombre): self
	{
		$this->trabajador_nombre = $trabajador_nombre;
		return $this;
	}
	
	public function getTrabajador_cedula(): ?string
	{
		return $this->trabajador_cedula;
	}
	
	public function setTrabajador_cedula(?string $trabajador_cedula): self
	{
		$this->trabajador_cedula = $trabajador_cedula;
		return $this;
	}
	
	public function getActivo_marca(): ?string
	{
		return $this->activo_marca;
	}
	
	public function setActivo_marca(?string $activo_marca): self
	{
		$this->activo_marca = $activo_marca;
		return $this;
	}
	
	public function getActivo_modelo(): ?string
	{
		return $this->activo_modelo;
	}
	
	public function setActivo_modelo(?string $activo_modelo): self
	{
		$this->activo_modelo = $activo_modelo;
		return $this;
	}
	
	public function getActivoNumeroSerie(): ?string
	{
		return $this->activo_numero_serie;
	}
	
	public function setActivoNumeroSerie(?string $activo_numero_serie): self
	{
		$this->activo_numero_serie = $activo_numero_serie;
		return $this;
	}
	
	public function getActivo_descripcion(): ?string
	{
		return $this->activo_descripcion;
	}
	
	public function setActivo_descripcion(?string $activo_descripcion): self
	{
		$this->activo_descripcion = $activo_descripcion;
		return $this;
	}

	public function getContenedor_descripcion(): ?string
	{
		return $this->contenedor_descripcion;
	}

	public function setContenedor_descripcion(?string $contenedor_descripcion): self
	{
		$this->contenedor_descripcion = $contenedor_descripcion;
		return $this;
	}
	
	// --- Métodos adicionales ---
	
	/**
	 * Incrementa la cantidad de un registro de inventario existente.
	 *
	 * @param int $id       ID del registro de inventario.
	 * @param int $incremento Cantidad a incrementar (debe ser positiva).
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si el incremento es negativo o si ocurre un error de base de datos.
	 */
	public static function incrementar_cantidad(int $id, int $incremento, PDO $conexion): bool
	{
		if ($incremento <= 0) {
			throw new Exception("El incremento debe ser un valor positivo.");
		}
		
		try {
			// Consulta para incrementar la cantidad
			$query = <<<SQL
            UPDATE inventario SET
                cantidad = cantidad + :incremento
            WHERE
                id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':incremento', $incremento, PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al incrementar cantidad de inventario (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Decrementa la cantidad de un registro de inventario existente.
	 *
	 * @param int $id       ID del registro de inventario.
	 * @param int $decremento Cantidad a decrementar (debe ser positiva).
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si el decremento es negativo, si resultaría en una cantidad negativa,
	 *                   o si ocurre un error de base de datos.
	 */
	public static function decrementar_cantidad(int $id, int $decremento, PDO $conexion): bool
	{
		if ($decremento <= 0) {
			throw new Exception("El decremento debe ser un valor positivo.");
		}
		
		try {
			// Primero verificamos que la cantidad actual sea suficiente
			$inventario = self::get($id, $conexion);
			if (!$inventario) {
				throw new Exception("No se encontró el registro de inventario con ID: $id");
			}
			
			$cantidadActual = $inventario->getCantidad();
			if ($cantidadActual < $decremento) {
				throw new Exception("No hay suficiente cantidad disponible para decrementar. Actual: $cantidadActual, Solicitado: $decremento");
			}
			
			// Consulta para decrementar la cantidad
			$query = <<<SQL
            UPDATE inventario SET
                cantidad = cantidad - :decremento
            WHERE
                id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':decremento', $decremento, PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al decrementar cantidad de inventario (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de activos que no están asociados a ningún contenedor en el inventario.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Activo.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_activos_no_asociados(PDO $conexion): array
	{
		try {
			// Consulta para obtener activos no asociados a ningún contenedor
			$query = <<<SQL
            SELECT a.*
            FROM activos a
            WHERE a.estado = 1
            AND NOT EXISTS (
                SELECT 1
                FROM inventario i
                WHERE i.id_activo = a.id
            )
            ORDER BY a.descripcion
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			
			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = Activo::construct($resultado);
			}
			return $listado;
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de activos no asociados: " . $e->getMessage());
		}
	}
	
	/**
	 * Actualiza un registro de inventario para devolver un activo al contenedor.
	 *
	 * @param int $id       ID del registro de inventario.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function devolver_activo(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el registro de inventario
			$query = <<<SQL
            UPDATE inventario SET
                id_trabajador = NULL,
                en_contenedor = 1
            WHERE
                id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al devolver activo al contenedor (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene una lista de registros de inventario filtrando por descripción de activo.
	 *
	 * @param string $texto_activo Texto para buscar en la descripción del activo.
	 * @param PDO $conexion Conexión PDO.
	 * @param int|null $id_contenedor ID del contenedor (opcional) para filtrar también por contenedor.
	 * @param int|null $en_contenedor Filtro para estado en_contenedor (1 = dentro, 0 = fuera).
	 * @param int|null $id_trabajador ID del trabajador asignado (-1 = no asignado, >0 = ID específico).
	 *
	 * @return array Array de objetos Inventario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_by_activo_descripcion(
		string $texto_activo, 
		PDO $conexion, 
		?int $id_contenedor = null,
		?int $en_contenedor = null,
		?int $id_trabajador = null
	): array
	{
		// Utilizar el método get_list con el filtro de texto_activo
		return self::get_list($conexion, $id_contenedor, null, $texto_activo, $en_contenedor, $id_trabajador);
	}
	
	/**
	 * Obtiene una lista filtrada de registros de inventario.
	 *
	 * @param PDO $conexion Conexión PDO.
	 * @param int|null $id_contenedor ID del contenedor para filtrar.
	 * @param int|null $en_contenedor Filtro para estado en_contenedor (1 = dentro, 0 = fuera).
	 * @param int|null $id_trabajador ID del trabajador asignado (-1 = no asignado, >0 = ID específico).
	 *
	 * @return array Array de objetos Inventario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list_filtrado(
		PDO $conexion, 
		?int $id_contenedor = null,
		?int $en_contenedor = null,
		?int $id_trabajador = null
	): array
	{
		// Utilizar el método get_list con los filtros proporcionados
		return self::get_list($conexion, $id_contenedor, null, null, $en_contenedor, $id_trabajador);
	}
	
	/**
	 * Actualiza un registro de inventario para prestar un activo a un trabajador.
	 *
	 * @param int $id           ID del registro de inventario.
	 * @param int $id_trabajador ID del trabajador al que se presta el activo.
	 * @param PDO $conexion     Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function prestar_a_trabajador(int $id, int $id_trabajador, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el registro de inventario
			$query = <<<SQL
            UPDATE inventario SET
                id_trabajador = :id_trabajador,
                en_contenedor = 0
            WHERE
                id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_trabajador', $id_trabajador, PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);
			
			return $statement->execute();
			
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al prestar activo a trabajador (ID: $id): " . $e->getMessage());
		}
	}
}
