<?php

// Iniciar sesión si es necesario
use App\classes\Inventario;
use App\classes\Contenedor;
use App\classes\Activo;
use App\classes\Trabajador;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

// Incluir archivos necesarios
require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Verificar que sea una petición POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
	http_response_code(405);
	echo json_encode(['success' => false, 'message' => 'Método no permitido']);
	exit;
}

// Verificar que sea una petición AJAX
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) !== 'xmlhttprequest') {
	http_response_code(400);
	echo json_encode(['success' => false, 'message' => 'Petición inválida']);
	exit;
}

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	http_response_code(503);
	echo json_encode(['success' => false, 'message' => 'Error crítico: No se pudo conectar a la base de datos.']);
	exit;
}

// Set response header to JSON
header('Content-Type: application/json');

#region region Handle Filters
// Process filter parameters from POST
$filtro_contenedor    = filter_input(INPUT_POST, 'contenedor', FILTER_VALIDATE_INT);
$filtro_activo_texto  = filter_input(INPUT_POST, 'activo_texto', FILTER_SANITIZE_SPECIAL_CHARS);
$filtro_en_contenedor = filter_input(INPUT_POST, 'en_contenedor', FILTER_SANITIZE_SPECIAL_CHARS);
$filtro_trabajador    = filter_input(INPUT_POST, 'trabajador', FILTER_VALIDATE_INT);

// Check if any filters are applied - only consider non-empty values
$filtros_aplicados = !empty($filtro_contenedor) || !empty($filtro_activo_texto) ||
                     ($filtro_en_contenedor !== '') ||
                     (!empty($filtro_trabajador) && $filtro_trabajador !== '');
#endregion Handle Filters

try {
	$inventarios = [];
	
	// Only fetch inventory data if filters are applied
	if ($filtros_aplicados) {
		// Get inventory data with all filters applied at the database level
		$inventarios = Inventario::get_list(
			$conexion,
			$filtro_contenedor,
			null,
			$filtro_activo_texto,
			$filtro_en_contenedor,
			$filtro_trabajador
		);
	}
	
	// Generate table HTML
	ob_start();
	
	if ($filtros_aplicados): 
		foreach ($inventarios as $inventario): ?>
			<tr data-inventario-id="<?php echo $inventario->getId(); ?>">
				<td class="text-center">
					<?php if ($inventario->getId_activo()): ?>
					<button type="button" class="btn btn-xs btn-info me-1 btn-ver-imagenes"
					        title="Ver Imágenes"
					        data-activoid="<?php echo $inventario->getId_activo(); ?>"
					        data-descripcion="<?php echo htmlspecialchars($inventario->getActivo_descripcion() ?? ''); ?>">
						<i class="fa fa-images"></i>
					</button>
					<?php endif; ?>

					<?php if ($inventario->getId_activo() && $inventario->getEn_contenedor() == 1): ?>
					<button type="button" class="btn btn-xs btn-warning me-1 btn-mover-activo"
					        title="Mover Activo"
					        data-inventario-id="<?php echo $inventario->getId(); ?>"
					        data-activo-id="<?php echo $inventario->getId_activo(); ?>"
					        data-activo-descripcion="<?php echo htmlspecialchars($inventario->getActivo_descripcion() ?? ''); ?>"
					        data-activo-marca="<?php echo htmlspecialchars($inventario->getActivo_marca() ?? 'N/A'); ?>"
					        data-activo-modelo="<?php echo htmlspecialchars($inventario->getActivo_modelo() ?? 'N/A'); ?>"
					        data-activo-serie="<?php echo htmlspecialchars($inventario->getActivoNumeroSerie() ?? 'N/A'); ?>"
					        data-contenedor-actual-id="<?php echo $inventario->getId_contenedor() ?? ''; ?>"
					        data-contenedor-actual-descripcion="<?php echo htmlspecialchars($inventario->getContenedor_descripcion() ?? 'N/A'); ?>">
						<i class="fa fa-exchange-alt"></i>
					</button>
					<?php endif; ?>
				</td>
				<td><?php echo htmlspecialchars($inventario->getContenedor_descripcion() ?? 'N/A'); ?></td>
				<td><?php echo htmlspecialchars($inventario->getActivo_descripcion() ?? 'N/A'); ?></td>
				<td><?php echo htmlspecialchars($inventario->getActivo_marca() ?? 'N/A'); ?></td>
				<td><?php echo htmlspecialchars($inventario->getActivo_modelo() ?? 'N/A'); ?></td>
				<td><?php echo htmlspecialchars($inventario->getActivoNumeroSerie() ?? 'N/A'); ?></td>
				<td class="text-center">
					<?php if ($inventario->getEn_contenedor() == 1): ?>
						<span class="badge bg-success">Dentro del contenedor</span>
					<?php else: ?>
						<span class="badge bg-danger">Fuera del contenedor</span>
					<?php endif; ?>
				</td>
				<td>
					<?php echo $inventario->getTrabajador_nombre() ? htmlspecialchars($inventario->getTrabajador_cedula() . ' - ' . $inventario->getTrabajador_nombre()) : '-- No asignado --'; ?>
				</td>
			</tr>
		<?php endforeach; 
		
		if (empty($inventarios)): ?>
			<tr>
				<td colspan="8" class="text-center">No se encontraron resultados para los filtros aplicados.</td>
			</tr>
		<?php endif; 
	else: ?>
		<tr>
			<td colspan="8" class="text-center">Aplique filtros para ver resultados.</td>
		</tr>
	<?php endif;
	
	$html = ob_get_clean();
	
	// Return success response with HTML
	echo json_encode([
		'success' => true,
		'html' => $html,
		'message' => 'Tabla actualizada correctamente'
	]);
	
} catch (PDOException $e) {
	// Specific handling for database errors
	error_log("Database error fetching inventory table: " . $e->getMessage());
	echo json_encode([
		'success' => false,
		'message' => 'Error de base de datos al obtener la lista de inventario.'
	]);
} catch (Exception $e) {
	// General error handling
	error_log("Error fetching inventory table: " . $e->getMessage());
	echo json_encode([
		'success' => false,
		'message' => 'Ocurrió un error inesperado al obtener la lista de inventario: ' . $e->getMessage()
	]);
}

?>
