<?php

// Iniciar sesión si es necesario
use App\classes\Inventario;
use App\classes\Contenedor;
use App\classes\Activo;
use App\classes\Trabajador;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en linventario.php.");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$inventarios          = [];     // Initialize as an empty array
$contenedores         = [];     // Initialize as an empty array
$activos              = [];     // Initialize as an empty array
$trabajadores         = [];     // Initialize as an empty array
$filtro_contenedor    = null;
$filtro_activo_texto  = null;
$filtro_en_contenedor = '';
$filtro_trabajador    = null;
$filtros_aplicados    = false;  // Flag to check if any filters are applied
#endregion init variables

#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Handle Filters
// Process filter parameters - ONLY POST method
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $filtro_contenedor    = filter_input(INPUT_POST, 'contenedor', FILTER_VALIDATE_INT);
    $filtro_activo_texto  = filter_input(INPUT_POST, 'activo_texto', FILTER_SANITIZE_SPECIAL_CHARS);
    $filtro_en_contenedor = filter_input(INPUT_POST, 'en_contenedor', FILTER_SANITIZE_SPECIAL_CHARS);
    $filtro_trabajador    = filter_input(INPUT_POST, 'trabajador', FILTER_VALIDATE_INT);

    // Check if any filters are applied - only consider non-empty values
    $filtros_aplicados = !empty($filtro_contenedor) || !empty($filtro_activo_texto) ||
                         ($filtro_en_contenedor !== '') ||
                         (!empty($filtro_trabajador) && $filtro_trabajador !== '');
}
// For GET requests (initial page load), no filters are applied - show no records
#endregion Handle Filters

#region try
try {
    // Get list of contenedores, activos, and trabajadores for filters
    $contenedores = Contenedor::get_list($conexion);
    $activos      = Activo::get_list($conexion);
    $trabajadores = Trabajador::get_list($conexion);

    // Only fetch inventory data if filters are applied
    if ($filtros_aplicados) {
        // Get inventory data with all filters applied at the database level
        $inventarios = Inventario::get_list(
            $conexion,
            $filtro_contenedor,
            null,
            $filtro_activo_texto,
            $filtro_en_contenedor,
            $filtro_trabajador
        );
    }
    
} catch (PDOException $e) {
	// Specific handling for database errors
	error_log("Database error fetching inventory: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de inventario.";
} catch (Exception $e) {
	// General error handling
	error_log("Error fetching inventory: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de inventario: " . $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/linventario.view.php';

?>
