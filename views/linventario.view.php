<?php
#region region DOCS

/** @var Inventario[] $inventarios */
/** @var Contenedor[] $contenedores */
/** @var Activo[] $activos */
/** @var Trabajador[] $trabajadores */
/** @var int|null $filtro_contenedor */
/** @var string|null $filtro_activo_texto */
/** @var int|null $filtro_en_contenedor */
/** @var int|null $filtro_trabajador */
/** @var bool $filtros_aplicados */

use App\classes\Inventario;
use App\classes\Contenedor;
use App\classes\Activo;
use App\classes\Trabajador;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Inventario</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Inventario</h4>
				<p class="mb-0 text-muted">Administra el inventario del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="sinventario" class="btn btn-danger me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Permite remover activos seleccionados de sus contenedores actuales"><i class="fa fa-minus-circle fa-fw me-1"></i> Sacar de contenedor</a>
				<a href="iinventario" class="btn btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Permite añadir nuevos activos a contenedores específicos"><i class="fa fa-plus-circle fa-fw me-1"></i> Agregar a contenedor</a>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FILTER PANEL ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Filtros
				</h4>
			</div>
			<div class="panel-body">
				<form action="linventario" method="POST" id="filter-form">
					<div class="row">
						<div class="col-md-3">
							<div class="mb-3">
								<label for="contenedor" class="form-label">Contenedor:</label>
								<select class="form-select" id="contenedor" name="contenedor">
									<option value="">-- Todos los contenedores --</option>
									<?php foreach ($contenedores as $contenedor): ?>
										<option value="<?php echo $contenedor->getId(); ?>" <?php echo ($filtro_contenedor == $contenedor->getId()) ? 'selected' : ''; ?>>
											<?php echo htmlspecialchars($contenedor->getDescripcion()); ?>
										</option>
									<?php endforeach; ?>
								</select>
							</div>
						</div>
						<div class="col-md-3">
							<div class="mb-3">
								<label for="activo_texto" class="form-label">Activo:</label>
								<input type="text" class="form-control" id="activo_texto" name="activo_texto" placeholder="Buscar por nombre de activo" value="<?php echo htmlspecialchars($filtro_activo_texto ?? ''); ?>">
							</div>
						</div>
						<div class="col-md-3">
							<div class="mb-3">
								<label for="en_contenedor" class="form-label">Estado:</label>
								<select class="form-select" id="en_contenedor" name="en_contenedor">
									<option value="">-- Todos --</option>
									<option value="1" <?php echo ($filtro_en_contenedor == 1) ? 'selected' : ''; ?>>Dentro del contenedor</option>
									<option value="0" <?php echo ($filtro_en_contenedor == 0) ? 'selected' : ''; ?>>Fuera del contenedor</option>
								</select>
							</div>
						</div>
						<div class="col-md-3">
							<div class="mb-3">
								<label for="trabajador" class="form-label">Trabajador Asignado:</label>
								<select class="form-select" id="trabajador" name="trabajador">
									<option value="">-- Todos --</option>
									<?php foreach ($trabajadores as $trabajador): ?>
										<option value="<?php echo $trabajador->getId(); ?>" <?php echo ($filtro_trabajador == $trabajador->getId()) ? 'selected' : ''; ?>>
											<?php echo htmlspecialchars($trabajador->getCedula() . ' - ' . $trabajador->getNombreCompleto()); ?>
										</option>
									<?php endforeach; ?>
								</select>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-12 text-end">
							<button type="submit" class="btn btn-primary">
								<i class="fa fa-search fa-fw me-1"></i> Filtrar
							</button>
							<a href="linventario" class="btn btn-default">
								<i class="fa fa-redo me-1"></i> Limpiar Filtros
							</a>
						</div>
					</div>
				</form>
			</div>
		</div>
		<?php #endregion FILTER PANEL ?>
		
		<?php #region region PANEL INVENTARIO ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h5 class="panel-title">
					Listado de Inventario
				</h5>
				<div class="ms-auto">
					<?php if ($filtros_aplicados): ?>
						<form method="POST" action="export_inventario" style="display: inline;">
							<input type="hidden" name="contenedor" value="<?php echo htmlspecialchars($filtro_contenedor ?? ''); ?>">
							<input type="hidden" name="activo_texto" value="<?php echo htmlspecialchars($filtro_activo_texto ?? ''); ?>">
							<input type="hidden" name="en_contenedor" value="<?php echo htmlspecialchars($filtro_en_contenedor ?? ''); ?>">
							<input type="hidden" name="trabajador" value="<?php echo htmlspecialchars($filtro_trabajador ?? ''); ?>">
							<button type="submit" class="btn btn-sm btn-primary me-2" title="Exportar a Excel">
								<i class="fa fa-file-excel fa-fw me-1"></i> Exportar a Excel
							</button>
						</form>
					<?php endif; ?>
					<a href="prestar_activos" class="btn btn-sm btn-danger me-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Permite asignar activos a trabajadores para uso temporal"><i class="fa fa-minus-circle fa-fw me-1"></i> Prestar activos</a>
					<a href="regresar_activos" class="btn btn-sm btn-success" data-bs-toggle="tooltip" data-bs-placement="top" title="Permite devolver activos prestados de vuelta a sus contenedores"><i class="fa fa-plus-circle fa-fw me-1"></i> Regresar activos</a>
				</div>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE INVENTARIO ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="text-center">Acciones</th>
						<th class="text-center">Contenedor</th>
						<th class="text-center">Activo</th>
						<th class="text-center">Marca</th>
						<th class="text-center">Modelo</th>
						<th class="text-center">Número Serie</th>
						<th class="text-center">Estado</th>
						<th class="text-center">Trabajador Asignado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="inventario-table-body">
					<?php if ($filtros_aplicados): ?>
						<?php foreach ($inventarios as $inventario): ?>
							<?php
								// All data is now available directly from the inventario object
								// No additional queries needed - data comes from optimized JOIN query
							?>
							<tr data-inventario-id="<?php echo $inventario->getId(); ?>">
								<td class="text-center">
									<?php if ($inventario->getId_activo()): ?>
									<button type="button" class="btn btn-xs btn-info me-1 btn-ver-imagenes"
									        title="Ver Imágenes"
									        data-activoid="<?php echo $inventario->getId_activo(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($inventario->getActivo_descripcion() ?? ''); ?>">
										<i class="fa fa-images"></i>
									</button>
									<?php endif; ?>

									<?php if ($inventario->getId_activo() && $inventario->getEn_contenedor() == 1): ?>
									<button type="button" class="btn btn-xs btn-warning me-1 btn-mover-activo"
									        title="Mover Activo"
									        data-inventario-id="<?php echo $inventario->getId(); ?>"
									        data-activo-id="<?php echo $inventario->getId_activo(); ?>"
									        data-activo-descripcion="<?php echo htmlspecialchars($inventario->getActivo_descripcion() ?? ''); ?>"
									        data-activo-marca="<?php echo htmlspecialchars($inventario->getActivo_marca() ?? 'N/A'); ?>"
									        data-activo-modelo="<?php echo htmlspecialchars($inventario->getActivo_modelo() ?? 'N/A'); ?>"
									        data-activo-serie="<?php echo htmlspecialchars($inventario->getActivoNumeroSerie() ?? 'N/A'); ?>"
									        data-contenedor-actual-id="<?php echo $inventario->getId_contenedor() ?? ''; ?>"
									        data-contenedor-actual-descripcion="<?php echo htmlspecialchars($inventario->getContenedor_descripcion() ?? 'N/A'); ?>">
										<i class="fa fa-exchange-alt"></i>
									</button>
									<?php endif; ?>
								</td>
								<td><?php echo htmlspecialchars($inventario->getContenedor_descripcion() ?? 'N/A'); ?></td>
								<td><?php echo htmlspecialchars($inventario->getActivo_descripcion() ?? 'N/A'); ?></td>
								<td><?php echo htmlspecialchars($inventario->getActivo_marca() ?? 'N/A'); ?></td>
								<td><?php echo htmlspecialchars($inventario->getActivo_modelo() ?? 'N/A'); ?></td>
								<td><?php echo htmlspecialchars($inventario->getActivoNumeroSerie() ?? 'N/A'); ?></td>
								<td class="text-center">
									<?php if ($inventario->getEn_contenedor() == 1): ?>
										<span class="badge bg-success">Dentro del contenedor</span>
									<?php else: ?>
										<span class="badge bg-danger">Fuera del contenedor</span>
									<?php endif; ?>
								</td>
								<td>
									<?php echo $inventario->getTrabajador_nombre() ? htmlspecialchars($inventario->getTrabajador_cedula() . ' - ' . $inventario->getTrabajador_nombre()) : '-- No asignado --'; ?>
								</td>
							</tr>
						<?php endforeach; ?>
						<?php if (empty($inventarios)): ?>
							<tr>
								<td colspan="8" class="text-center">No se encontraron resultados para los filtros aplicados.</td>
							</tr>
						<?php endif; ?>
					<?php else: ?>
						<tr>
							<td colspan="8" class="text-center">Aplique filtros para ver resultados.</td>
						</tr>
					<?php endif; ?>
					
					</tbody>
				</table>
				<?php #endregion TABLE INVENTARIO ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL INVENTARIO ?>
	</div>
	<!-- END #content -->
	
	
	<!-- Modal for displaying images -->
	<div class="modal fade" id="imagenes-modal" tabindex="-1" role="dialog" aria-labelledby="imagenes-modal-label" aria-hidden="true">
		<div class="modal-dialog modal-lg" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="imagenes-modal-label">Imágenes del Activo</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div id="imagenes-container" class="d-flex flex-wrap gap-2">
						<!-- Images will be loaded here -->
					</div>
					<div id="no-imagenes-message" class="text-center p-3 d-none">
						<p>Este activo no tiene imágenes asociadas.</p>
					</div>
					<div id="loading-imagenes" class="text-center p-3">
						<span class="spinner-border spinner-border-sm"></span> Cargando imágenes...
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Modal for moving assets -->
	<div class="modal fade" id="mover-activo-modal" tabindex="-1" role="dialog" aria-labelledby="mover-activo-modal-label" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="mover-activo-modal-label">Mover Activo</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div id="mover-activo-error" class="alert alert-danger d-none"></div>

					<div class="mb-3">
						<h6>Información del Activo</h6>
						<div class="card">
							<div class="card-body">
								<p class="mb-1"><strong>Descripción:</strong> <span id="modal-activo-descripcion"></span></p>
								<p class="mb-1"><strong>Marca:</strong> <span id="modal-activo-marca"></span></p>
								<p class="mb-1"><strong>Modelo:</strong> <span id="modal-activo-modelo"></span></p>
								<p class="mb-0"><strong>Número de Serie:</strong> <span id="modal-activo-serie"></span></p>
							</div>
						</div>
					</div>

					<div class="mb-3">
						<h6>Contenedor Actual</h6>
						<div class="alert alert-info">
							<span id="modal-contenedor-actual"></span>
						</div>
					</div>

					<form id="mover-activo-form">
						<div class="mb-3">
							<label for="destino-contenedor" class="form-label">Contenedor Destino <span class="text-danger">*</span></label>
							<select class="form-select" id="destino-contenedor" name="destino_contenedor_id" required>
								<option value="">-- Seleccione un contenedor --</option>
								<?php foreach ($contenedores as $contenedor_option): ?>
									<option value="<?php echo $contenedor_option->getId(); ?>">
										<?php echo htmlspecialchars($contenedor_option->getDescripcion()); ?>
									</option>
								<?php endforeach; ?>
							</select>
							<div class="invalid-feedback">Por favor seleccione un contenedor destino.</div>
						</div>
						<input type="hidden" id="inventario-id" name="inventario_id">
					</form>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
					<button type="button" class="btn btn-primary" id="btn-confirmar-mover">
						<i class="fa fa-exchange-alt fa-fw me-1"></i> Mover Activo
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script src="<?php echo RUTA_RESOURCES ?>js/linventario.js"></script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
